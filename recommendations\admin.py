from django.contrib import admin
from .models import (
    Recommendation, UserSimilarity, ProductSimilarity, 
    RecommendationFeedback, MLModel
)


@admin.register(Recommendation)
class RecommendationAdmin(admin.ModelAdmin):
    list_display = ('user', 'product', 'recommendation_type', 'score', 'is_active', 'created_at')
    list_filter = ('recommendation_type', 'is_active', 'created_at')
    search_fields = ('user__email', 'product__name')
    readonly_fields = ('created_at', 'updated_at', 'click_through_rate', 'conversion_rate')
    
    fieldsets = (
        ('Recommendation Details', {
            'fields': ('user', 'product', 'recommendation_type', 'score', 'reason', 'is_active')
        }),
        ('Performance Metrics', {
            'fields': ('view_count', 'click_count', 'conversion_count', 'click_through_rate', 'conversion_rate'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserSimilarity)
class UserSimilarityAdmin(admin.ModelAdmin):
    list_display = ('user1', 'user2', 'similarity_score', 'algorithm', 'updated_at')
    list_filter = ('algorithm', 'updated_at')
    search_fields = ('user1__email', 'user2__email')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(ProductSimilarity)
class ProductSimilarityAdmin(admin.ModelAdmin):
    list_display = ('product1', 'product2', 'similarity_score', 'algorithm', 'updated_at')
    list_filter = ('algorithm', 'updated_at')
    search_fields = ('product1__name', 'product2__name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(RecommendationFeedback)
class RecommendationFeedbackAdmin(admin.ModelAdmin):
    list_display = ('user', 'recommendation', 'feedback_type', 'created_at')
    list_filter = ('feedback_type', 'created_at')
    search_fields = ('user__email', 'recommendation__product__name')
    readonly_fields = ('created_at',)


@admin.register(MLModel)
class MLModelAdmin(admin.ModelAdmin):
    list_display = ('name', 'model_type', 'version', 'is_active', 'accuracy_score', 'created_at')
    list_filter = ('model_type', 'is_active', 'created_at')
    search_fields = ('name', 'algorithm')
    readonly_fields = ('created_at',)
    
    fieldsets = (
        ('Model Information', {
            'fields': ('name', 'model_type', 'version', 'algorithm', 'is_active')
        }),
        ('Training Details', {
            'fields': ('parameters', 'training_data_size', 'model_file_path')
        }),
        ('Performance Metrics', {
            'fields': ('accuracy_score', 'precision_score', 'recall_score', 'f1_score'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
