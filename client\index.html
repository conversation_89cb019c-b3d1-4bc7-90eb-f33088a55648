<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopWave - Premium E-commerce Store</title>
    <meta name="description" content="Discover amazing products at ShopWave - Your premium shopping destination" />
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>ShopWave</h1>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="#products" class="nav-link">Products</a>
                <a href="#cart" class="nav-link">Cart (<span id="cart-count">0</span>)</a>
                <a href="#" class="nav-link" id="auth-link" onclick="handleAuthClick()">Login</a>
            </div>
            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content Area -->
    <main>
        <!-- Home Section -->
        <div class="hero">
            <div class="hero-content">
                <h1 class="hero-title">
                    Discover
                    <span class="gradient-text">Amazing Products</span>
                </h1>
                <p class="hero-description">
                    Shop the latest trends with premium quality products at unbeatable prices.
                </p>
                <div class="search-container">
                    <input type="text" id="search-input" placeholder="Search products..." class="search-input">
                    <button class="search-btn" onclick="filterProducts()">🔍</button>
                </div>
                <button class="cta-btn" onclick="showSection('products')">Shop Now</button>
            </div>
        </div>

        <!-- Featured Products -->
        <div class="featured-section">
            <h2>Featured Products</h2>
            <div class="products-grid" id="featured-products">
                <!-- Products will be loaded here -->
            </div>
        </div>

        <!-- Recommendations Section (only shown when logged in) -->
        <div class="featured-section" id="recommendations-section" style="display: none;">
            <h2>Recommended for You</h2>
            <div class="products-grid" id="recommended-products">
                <!-- Recommended products will be loaded here -->
            </div>
        </div>

        <!-- Trending Products -->
        <div class="featured-section">
            <h2>Trending Now</h2>
            <div class="products-grid" id="trending-products">
                <!-- Trending products will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Product Detail Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="product-detail" id="product-detail">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeModal('login-modal')">&times;</span>
            <h2>🔐 Login</h2>
            <form id="login-form" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="login-email">Email:</label>
                    <input type="email" id="login-email" required>
                </div>
                <div class="form-group">
                    <label for="login-password">Password:</label>
                    <input type="password" id="login-password" required>
                </div>
                <button type="submit" class="cta-btn">Login</button>
                <p style="text-align: center; margin-top: 1rem;">
                    Don't have an account?
                    <a href="#" onclick="showRegisterModal()">Register here</a>
                </p>
            </form>
        </div>
    </div>

    <!-- Register Modal -->
    <div id="register-modal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeModal('register-modal')">&times;</span>
            <h2>📝 Register</h2>
            <form id="register-form" onsubmit="handleRegister(event)">
                <div class="form-group">
                    <label for="register-first-name">First Name:</label>
                    <input type="text" id="register-first-name" required>
                </div>
                <div class="form-group">
                    <label for="register-last-name">Last Name:</label>
                    <input type="text" id="register-last-name" required>
                </div>
                <div class="form-group">
                    <label for="register-username">Username:</label>
                    <input type="text" id="register-username" required>
                </div>
                <div class="form-group">
                    <label for="register-email">Email:</label>
                    <input type="email" id="register-email" required>
                </div>
                <div class="form-group">
                    <label for="register-password">Password:</label>
                    <input type="password" id="register-password" required>
                </div>
                <button type="submit" class="cta-btn">Register</button>
                <p style="text-align: center; margin-top: 1rem;">
                    Already have an account?
                    <a href="#" onclick="showLoginModal()">Login here</a>
                </p>
            </form>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div id="checkout-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <span class="close" onclick="closeModal('checkout-modal')">&times;</span>
            <h2>🛒 Checkout</h2>
            <div id="checkout-content">
                <!-- Checkout form will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Payment Result Modal -->
    <div id="payment-result-modal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeModal('payment-result-modal')">&times;</span>
            <div id="payment-result-content">
                <!-- Payment result will be shown here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>ShopWave</h3>
                <p>Your premium shopping destination for quality products.</p>
            </div>
            <div class="footer-section">
                <h4>Quick Links</h4>
                <a href="#home">Home</a>
                <a href="#products">Products</a>
                <a href="#cart">Cart</a>
            </div>
            <div class="footer-section">
                <h4>Contact</h4>
                <p><EMAIL></p>
                <p>1-800-SHOPWAVE</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 ShopWave. All rights reserved.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
