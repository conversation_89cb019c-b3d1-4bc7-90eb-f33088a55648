from django.contrib import admin
from .models import Payment, PaymentMethod, Refund, PaymentWebhook


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'order', 'amount', 'currency', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'currency', 'created_at')
    search_fields = ('user__email', 'order__order_number', 'gateway_reference')
    readonly_fields = ('id', 'created_at', 'updated_at', 'processed_at')
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Payment Information', {
            'fields': ('id', 'user', 'order', 'amount', 'currency', 'payment_method', 'status')
        }),
        ('Gateway Details', {
            'fields': ('gateway_reference', 'gateway_response'),
            'classes': ('collapse',)
        }),
        ('Financial Details', {
            'fields': ('transaction_fee', 'net_amount')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'processed_at'),
            'classes': ('collapse',)
        }),
        ('Additional', {
            'fields': ('failure_reason', 'notes'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('user', 'card_type', 'last_four_digits', 'expiry_month', 'expiry_year', 'is_default', 'is_active')
    list_filter = ('card_type', 'is_active', 'is_default', 'created_at')
    search_fields = ('user__email', 'cardholder_name', 'last_four_digits')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    list_display = ('id', 'payment', 'user', 'amount', 'reason', 'status', 'created_at')
    list_filter = ('status', 'reason', 'created_at')
    search_fields = ('user__email', 'payment__gateway_reference', 'gateway_reference')
    readonly_fields = ('id', 'created_at', 'updated_at', 'processed_at')
    
    fieldsets = (
        ('Refund Information', {
            'fields': ('id', 'payment', 'user', 'amount', 'reason', 'description', 'status')
        }),
        ('Gateway Details', {
            'fields': ('gateway_reference', 'gateway_response'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'processed_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PaymentWebhook)
class PaymentWebhookAdmin(admin.ModelAdmin):
    list_display = ('gateway', 'event_type', 'event_id', 'reference', 'processed', 'created_at')
    list_filter = ('gateway', 'event_type', 'processed', 'created_at')
    search_fields = ('event_id', 'reference')
    readonly_fields = ('created_at', 'processed_at')
    date_hierarchy = 'created_at'
