Watching for file changes with StatReloader
"GET /api/products/ HTTP/1.1" 200 500
- Broken pipe from ('127.0.0.1', 12913)
"GET /api/products/categories/ HTTP/1.1" 200 522
- Broken pipe from ('127.0.0.1', 12937)
"GET /api/recommendations/trending/ HTTP/1.1" 200 406
"GET /api/products/ HTTP/1.1" 200 500
"GET /api/products/categories/ HTTP/1.1" 200 522
"GET /api/products/featured/ HTTP/1.1" 200 432
"GET /api/products/ HTTP/1.1" 200 500
"GET /api/products/ HTTP/1.1" 200 500
- Broken pipe from ('127.0.0.1', 13507)
"GET /api/products/ HTTP/1.1" 200 2494
"GET /api/recommendations/trending/ HTTP/1.1" 200 2037
"GET /api/products/ HTTP/1.1" 200 2494
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\payments\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\payments\urls.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\Project 911\Ecommerce\server\orders\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/products/ HTTP/1.1" 200 2494
"GET /api/products/featured/ HTTP/1.1" 200 1181
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4170
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/css/login.css HTTP/1.1" 200 951
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3169
Watching for file changes with StatReloader
