# AI-Powered E-commerce Recommendation System

A full-stack e-commerce platform with AI-powered product recommendations using Django REST Framework for the backend and vanilla JavaScript for the frontend.

## Project Structure

```
├── client/                 # Frontend (Vanilla JavaScript)
│   ├── index.html         # Main HTML file
│   ├── script.js          # JavaScript functionality
│   └── styles.css         # CSS styles
├── server/                # Backend (Django REST Framework)
│   ├── manage.py          # Django management script
│   ├── requirements.txt   # Python dependencies
│   ├── .env              # Environment variables
│   ├── seed_data.py      # Database seeding script
│   ├── ecommerce_backend/ # Main Django project
│   ├── accounts/         # User authentication & profiles
│   ├── products/         # Product management
│   ├── orders/           # Shopping cart & orders
│   ├── recommendations/  # AI recommendation engine
│   └── payments/         # Payment processing (Paystack)
└── README.md
```

## Features

### Backend Features
- **User Authentication**: JWT-based authentication with registration, login, logout
- **Product Management**: CRUD operations for products and categories
- **Shopping Cart**: Add/remove items, quantity management
- **Order Management**: Order creation and tracking
- **AI Recommendations**: Collaborative filtering and content-based recommendations
- **User Behavior Tracking**: Track user interactions for ML training
- **Payment Integration**: Paystack payment gateway integration
- **Admin Panel**: Django admin for content management

### Frontend Features
- **Responsive Design**: Mobile-first responsive design
- **Product Catalog**: Browse products with filtering and search
- **Shopping Cart**: Interactive cart with real-time updates
- **User Authentication**: Login/register forms
- **Product Recommendations**: Display AI-powered recommendations
- **Modern UI**: Clean, modern interface with smooth animations

## Technology Stack

### Backend
- **Framework**: Django 4.2.7 with Django REST Framework
- **Database**: PostgreSQL
- **Authentication**: JWT (Simple JWT)
- **ML Libraries**: Scikit-learn, Pandas, NumPy
- **Payment**: Paystack API
- **Deployment**: Gunicorn, WhiteNoise

### Frontend
- **Languages**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Unicode emojis and symbols
- **Images**: Unsplash API for product images

## Setup Instructions

### Prerequisites
- Python 3.8+
- PostgreSQL
- Node.js (optional, for frontend development)

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ecommerce-recommendation-system
   ```

2. **Create virtual environment**
   ```bash
   cd server
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup PostgreSQL database**
   - Create a PostgreSQL database named `ecommerce_recommendation_db`
   - Update the `.env` file with your database credentials

5. **Configure environment variables**
   ```bash
   # Copy and edit the .env file
   cp .env.example .env
   ```
   
   Update the following variables:
   ```
   SECRET_KEY=your-secret-key
   DEBUG=True
   DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce_recommendation_db
   PAYSTACK_SECRET_KEY=your-paystack-secret-key
   PAYSTACK_PUBLIC_KEY=your-paystack-public-key
   ```

6. **Run migrations**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

7. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

8. **Seed sample data**
   ```bash
   python manage.py shell < seed_data.py
   ```

9. **Run development server**
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to client directory**
   ```bash
   cd client
   ```

2. **Open in browser**
   - Open `index.html` in your browser, or
   - Use a local server like Live Server in VS Code

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `GET /api/auth/profile/` - Get user profile
- `PUT /api/auth/profile/` - Update user profile

### Products
- `GET /api/products/` - List all products
- `GET /api/products/featured/` - List featured products
- `GET /api/products/categories/` - List categories
- `GET /api/products/<slug>/` - Get product details
- `GET /api/products/search/` - Search products

### Orders
- `GET /api/orders/cart/` - Get user's cart
- `POST /api/orders/cart/add/` - Add item to cart
- `PUT /api/orders/cart/update/` - Update cart item
- `DELETE /api/orders/cart/remove/` - Remove item from cart

### Recommendations
- `GET /api/recommendations/` - Get user recommendations
- `POST /api/recommendations/feedback/` - Submit recommendation feedback

## Development

### Running Tests
```bash
cd server
python manage.py test
```

### Code Style
- Backend: Follow PEP 8 guidelines
- Frontend: Use ESLint and Prettier for consistent formatting

### Database Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

## Deployment

### Backend Deployment (Heroku)
1. Install Heroku CLI
2. Create Heroku app
3. Set environment variables
4. Deploy using Git

### Frontend Deployment
- Deploy to Netlify, Vercel, or any static hosting service
- Update API endpoints to point to production backend

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, email <EMAIL> or create an issue in the repository.
