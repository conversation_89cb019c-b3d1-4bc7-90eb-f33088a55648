* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(147, 51, 234, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    background: linear-gradient(135deg, #9333ea, #2563eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #4b5563;
    font-weight: 500;
    transition: color 0.3s ease;
    cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
    color: #9333ea;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Sections */
.section {
    display: none;
    padding-top: 80px;
    min-height: 100vh;
}

.section.active {
    display: block;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 80px 20px;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(37, 99, 235, 0.1));
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.gradient-text {
    background: linear-gradient(135deg, #9333ea, #2563eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: #6b7280;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-container {
    display: flex;
    max-width: 400px;
    margin: 2rem auto;
    background: white;
    border-radius: 50px;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 10px 15px;
    font-size: 1rem;
    background: transparent;
}

.search-btn {
    background: linear-gradient(135deg, #9333ea, #2563eb);
    border: none;
    padding: 10px 15px;
    border-radius: 50px;
    color: white;
    cursor: pointer;
    font-size: 1rem;
}

.cta-btn {
    background: linear-gradient(135deg, #9333ea, #2563eb);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
}

/* Featured Section */
.featured-section {
    padding: 60px 20px;
    text-align: center;
}

.featured-section h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1f2937;
}

/* Products */
.products-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.products-header h1 {
    font-size: 2.5rem;
    color: #1f2937;
}

.filters {
    display: flex;
    gap: 1rem;
}

.filters select {
    padding: 10px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 50px;
    background: white;
    font-size: 1rem;
    cursor: pointer;
    outline: none;
    transition: border-color 0.3s ease;
}

.filters select:focus {
    border-color: #9333ea;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.product-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.stars {
    color: #fbbf24;
}

.product-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.product-description {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #9333ea;
}

.add-to-cart {
    background: linear-gradient(135deg, #9333ea, #2563eb);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.add-to-cart:hover {
    transform: scale(1.05);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 20px;
    top: 15px;
}

.close:hover {
    color: #000;
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.product-detail-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

.product-detail-info h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #1f2937;
}

.product-detail-price {
    font-size: 2rem;
    font-weight: 700;
    color: #9333ea;
    margin-bottom: 1rem;
}

.quantity-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.quantity-btn {
    background: #f3f4f6;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
}

.quantity-input {
    width: 60px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    padding: 10px;
}

/* Cart */
.cart-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.cart-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
    color: #1f2937;
}

.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.cart-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cart-item-price {
    color: #9333ea;
    font-weight: 700;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-summary {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    height: fit-content;
    position: sticky;
    top: 100px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.summary-total {
    border-top: 2px solid #e5e7eb;
    padding-top: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.checkout-btn {
    width: 100%;
    background: linear-gradient(135deg, #9333ea, #2563eb);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
    transition: transform 0.3s ease;
}

.checkout-btn:hover {
    transform: translateY(-2px);
}

.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.empty-cart h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #f9fafb;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #9333ea;
}

.footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .products-header {
        flex-direction: column;
        align-items: stretch;
    }

    .filters {
        justify-content: center;
    }

    .product-detail {
        grid-template-columns: 1fr;
    }

    .cart-content {
        grid-template-columns: 1fr;
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .hero {
        padding: 40px 20px;
    }

    .hero-title {
        font-size: 2rem;
    }
}
