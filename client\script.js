// Product data
const products = [
    {
        id: 1,
        name: "Premium Wireless Headphones",
        price: 299.99,
        image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop",
        rating: 4.8,
        category: "Electronics",
        description: "High-quality wireless headphones with active noise cancellation technology. Perfect for music lovers and professionals alike."
    },
    {
        id: 2,
        name: "Smart Fitness Watch",
        price: 199.99,
        image: "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop",
        rating: 4.6,
        category: "Electronics",
        description: "Track your fitness goals with precision using this advanced smartwatch. Monitor heart rate, steps, and more."
    },
    {
        id: 3,
        name: "Minimalist Backpack",
        price: 89.99,
        image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop",
        rating: 4.7,
        category: "Fashion",
        description: "Stylish and functional backpack for everyday use. Perfect for work, travel, or casual outings."
    },
    {
        id: 4,
        name: "Organic Coffee Beans",
        price: 24.99,
        image: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=400&h=400&fit=crop",
        rating: 4.9,
        category: "Food",
        description: "Premium organic coffee beans from sustainable farms. Rich flavor and ethically sourced."
    },
    {
        id: 5,
        name: "Wireless Charging Pad",
        price: 49.99,
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=400&fit=crop",
        rating: 4.5,
        category: "Electronics",
        description: "Fast wireless charging for all compatible devices. Sleek design with LED indicators."
    },
    {
        id: 6,
        name: "Eco-Friendly Water Bottle",
        price: 34.99,
        image: "https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=400&fit=crop",
        rating: 4.8,
        category: "Lifestyle",
        description: "Sustainable stainless steel water bottle. Keeps drinks cold for 24h, hot for 12h."
    }
];

// Cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let currentSection = 'home';

// DOM elements
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('.section');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const searchInput = document.getElementById('search-input');
const categoryFilter = document.getElementById('category-filter');
const sortFilter = document.getElementById('sort-filter');
const modal = document.getElementById('product-modal');
const closeModal = document.querySelector('.close');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    displayFeaturedProducts();
    displayAllProducts();
    setupEventListeners();
});

// Event listeners
function setupEventListeners() {
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('href').substring(1);
            showSection(section);
        });
    });

    // Mobile menu
    hamburger.addEventListener('click', () => {
        navMenu.classList.toggle('active');
    });

    // Search
    searchInput.addEventListener('input', filterProducts);
    
    // Filters
    categoryFilter.addEventListener('change', filterProducts);
    sortFilter.addEventListener('change', filterProducts);

    // Modal
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Navigation functions
function showSection(sectionName) {
    // Update navigation
    navLinks.forEach(link => link.classList.remove('active'));
    sections.forEach(section => section.classList.remove('active'));
    
    document.getElementById(sectionName).classList.add('active');
    document.querySelector(`[href="#${sectionName}"]`).classList.add('active');
    
    currentSection = sectionName;
    navMenu.classList.remove('active');
    
    // Load section-specific content
    if (sectionName === 'cart') {
        displayCart();
    }
}

// Product display functions
function displayFeaturedProducts() {
    const featuredContainer = document.getElementById('featured-products');
    const featuredProducts = products.slice(0, 6);
    
    featuredContainer.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');
}

function displayAllProducts() {
    const productsContainer = document.getElementById('all-products');
    productsContainer.innerHTML = products.map(product => createProductCard(product)).join('');
}

function createProductCard(product) {
    const stars = '★'.repeat(Math.floor(product.rating)) + '☆'.repeat(5 - Math.floor(product.rating));
    
    return `
        <div class="product-card" onclick="showProductDetail(${product.id})">
            <img src="${product.image}" alt="${product.name}" class="product-image">
            <div class="product-info">
                <div class="product-rating">
                    <span class="stars">${stars}</span>
                    <span>(${product.rating})</span>
                </div>
                <h3 class="product-name">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-footer">
                    <span class="product-price">$${product.price}</span>
                    <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id})">
                        🛒 Add
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Product detail modal
function showProductDetail(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const stars = '★'.repeat(Math.floor(product.rating)) + '☆'.repeat(5 - Math.floor(product.rating));
    
    document.getElementById('product-detail').innerHTML = `
        <div>
            <img src="${product.image}" alt="${product.name}" class="product-detail-image">
        </div>
        <div class="product-detail-info">
            <h2>${product.name}</h2>
            <div class="product-rating">
                <span class="stars">${stars}</span>
                <span>(${product.rating}) • ${product.category}</span>
            </div>
            <div class="product-detail-price">$${product.price}</div>
            <p>${product.description}</p>
            
            <div class="quantity-selector">
                <span>Quantity:</span>
                <button class="quantity-btn" onclick="changeQuantity(-1)">-</button>
                <input type="number" id="quantity" class="quantity-input" value="1" min="1">
                <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
            </div>
            
            <button class="cta-btn" onclick="addToCartWithQuantity(${product.id})">
                🛒 Add to Cart
            </button>
        </div>
    `;
    
    modal.style.display = 'block';
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('quantity');
    let quantity = parseInt(quantityInput.value) + change;
    if (quantity < 1) quantity = 1;
    quantityInput.value = quantity;
}

// Cart functions
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        cart.push({...product, cartId: Date.now()});
        updateCartCount();
        saveCart();
        showNotification(`${product.name} added to cart!`);
    }
}

function addToCartWithQuantity(productId) {
    const product = products.find(p => p.id === productId);
    const quantity = parseInt(document.getElementById('quantity').value);
    
    if (product) {
        for (let i = 0; i < quantity; i++) {
            cart.push({...product, cartId: Date.now() + i});
        }
        updateCartCount();
        saveCart();
        modal.style.display = 'none';
        showNotification(`${quantity} x ${product.name} added to cart!`);
    }
}

function removeFromCart(cartId) {
    cart = cart.filter(item => item.cartId !== cartId);
    updateCartCount();
    saveCart();
    displayCart();
}

function updateCartCount() {
    document.getElementById('cart-count').textContent = cart.length;
}

function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

function displayCart() {
    const cartItemsContainer = document.getElementById('cart-items');
    const cartSummaryContainer = document.getElementById('cart-summary');
    
    if (cart.length === 0) {
        cartItemsContainer.innerHTML = `
            <div class="empty-cart">
                <h3>Your cart is empty</h3>
                <p>Start shopping to add items to your cart</p>
                <button class="cta-btn" onclick="showSection('products')">Shop Now</button>
            </div>
        `;
        cartSummaryContainer.innerHTML = '';
        return;
    }
    
    // Group items by product ID
    const groupedItems = cart.reduce((acc, item) => {
        const existing = acc.find(group => group.id === item.id);
        if (existing) {
            existing.quantity += 1;
            existing.items.push(item);
        } else {
            acc.push({
                ...item,
                quantity: 1,
                items: [item]
            });
        }
        return acc;
    }, []);
    
    cartItemsContainer.innerHTML = groupedItems.map(group => `
        <div class="cart-item">
            <img src="${group.image}" alt="${group.name}" class="cart-item-image">
            <div class="cart-item-info">
                <div class="cart-item-name">${group.name}</div>
                <div class="cart-item-price">$${group.price}</div>
            </div>
            <div class="cart-item-controls">
                <button class="quantity-btn" onclick="updateCartQuantity(${group.id}, ${group.quantity - 1})">-</button>
                <span>${group.quantity}</span>
                <button class="quantity-btn" onclick="updateCartQuantity(${group.id}, ${group.quantity + 1})">+</button>
                <button class="quantity-btn" onclick="removeProductFromCart(${group.id})" style="background: #ef4444; color: white; margin-left: 1rem;">×</button>
            </div>
        </div>
    `).join('');
    
    const subtotal = cart.reduce((sum, item) => sum + item.price, 0);
    const tax = subtotal * 0.08;
    const shipping = subtotal > 50 ? 0 : 9.99;
    const total = subtotal + tax + shipping;
    
    cartSummaryContainer.innerHTML = `
        <h3>Order Summary</h3>
        <div class="summary-row">
            <span>Subtotal (${cart.length} items):</span>
            <span>$${subtotal.toFixed(2)}</span>
        </div>
        <div class="summary-row">
            <span>Shipping:</span>
            <span style="color: ${shipping === 0 ? '#10b981' : '#374151'}">${shipping === 0 ? 'Free' : '$' + shipping.toFixed(2)}</span>
        </div>
        <div class="summary-row">
            <span>Tax:</span>
            <span>$${tax.toFixed(2)}</span>
        </div>
        <div class="summary-row summary-total">
            <span>Total:</span>
            <span>$${total.toFixed(2)}</span>
        </div>
        <button class="checkout-btn" onclick="checkout()">
            Proceed to Checkout
        </button>
        <button class="checkout-btn" onclick="clearCart()" style="background: #ef4444; margin-top: 0.5rem;">
            Clear Cart
        </button>
    `;
}

function updateCartQuantity(productId, newQuantity) {
    if (newQuantity <= 0) {
        removeProductFromCart(productId);
        return;
    }
    
    const productItems = cart.filter(item => item.id === productId);
    const currentQuantity = productItems.length;
    
    if (newQuantity > currentQuantity) {
        // Add more items
        const product = products.find(p => p.id === productId);
        for (let i = 0; i < newQuantity - currentQuantity; i++) {
            cart.push({...product, cartId: Date.now() + i});
        }
    } else {
        // Remove items
        const itemsToRemove = currentQuantity - newQuantity;
        for (let i = 0; i < itemsToRemove; i++) {
            const index = cart.findIndex(item => item.id === productId);
            if (index > -1) cart.splice(index, 1);
        }
    }
    
    updateCartCount();
    saveCart();
    displayCart();
}

function removeProductFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartCount();
    saveCart();
    displayCart();
}

function clearCart() {
    cart = [];
    updateCartCount();
    saveCart();
    displayCart();
    showNotification('Cart cleared!');
}

function checkout() {
    if (cart.length === 0) return;
    
    alert('Thank you for your purchase! This is a demo store.');
    clearCart();
}

// Filter and search functions
function filterProducts() {
    const searchTerm = searchInput.value.toLowerCase();
    const category = categoryFilter.value;
    const sortBy = sortFilter.value;
    
    let filteredProducts = products.filter(product => {
        const matchesSearch = product.name.toLowerCase().includes(searchTerm) ||
                            product.category.toLowerCase().includes(searchTerm);
        const matchesCategory = category === 'all' || product.category === category;
        return matchesSearch && matchesCategory;
    });
    
    // Sort products
    filteredProducts.sort((a, b) => {
        switch (sortBy) {
            case 'price-low':
                return a.price - b.price;
            case 'price-high':
                return b.price - a.price;
            case 'rating':
                return b.rating - a.rating;
            default:
                return a.name.localeCompare(b.name);
        }
    });
    
    const productsContainer = document.getElementById('all-products');
    if (filteredProducts.length === 0) {
        productsContainer.innerHTML = `
            <div style="grid-column: 1/-1; text-align: center; padding: 4rem;">
                <h3>No products found</h3>
                <p>Try adjusting your search or filter criteria.</p>
            </div>
        `;
    } else {
        productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
    }
}

// Notification function
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #9333ea, #2563eb);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
