// API Configuration
const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Global variables
let products = [];
let categories = [];
let currentUser = null;

// API Functions
async function fetchProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/`);
        const data = await response.json();
        products = data.results || [];
        return products;
    } catch (error) {
        console.error('Error fetching products:', error);
        return [];
    }
}

async function fetchCategories() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/categories/`);
        const data = await response.json();
        categories = data.results || [];
        return categories;
    } catch (error) {
        console.error('Error fetching categories:', error);
        return [];
    }
}

async function fetchFeaturedProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/products/featured/`);
        const data = await response.json();
        return data.results || [];
    } catch (error) {
        console.error('Error fetching featured products:', error);
        return [];
    }
}

async function fetchTrendingProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/recommendations/trending/`);
        const data = await response.json();
        return data.products || [];
    } catch (error) {
        console.error('Error fetching trending products:', error);
        return [];
    }
}

async function trackUserBehavior(action, productId = null, metadata = {}) {
    if (!currentUser) return;

    try {
        await fetch(`${API_BASE_URL}/auth/behavior/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            credentials: 'include',
            body: JSON.stringify({
                action: action,
                product_id: productId,
                metadata: metadata
            })
        });
    } catch (error) {
        console.error('Error tracking behavior:', error);
    }
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let currentSection = 'home';

// DOM elements
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('.section');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const searchInput = document.getElementById('search-input');
const categoryFilter = document.getElementById('category-filter');
const sortFilter = document.getElementById('sort-filter');
const modal = document.getElementById('product-modal');
const closeModal = document.querySelector('.close');

// Initialize app
document.addEventListener('DOMContentLoaded', async function() {
    updateCartCount();
    setupEventListeners();

    // Load data from API
    await loadInitialData();
});

async function loadInitialData() {
    try {
        // Show loading state
        showLoading();

        // Load categories and products
        await Promise.all([
            fetchCategories(),
            fetchProducts()
        ]);

        // Update category filter
        updateCategoryFilter();

        // Display products
        await displayFeaturedProducts();
        await displayAllProducts();

        hideLoading();
    } catch (error) {
        console.error('Error loading initial data:', error);
        hideLoading();
        showError('Failed to load data. Please refresh the page.');
    }
}

function showLoading() {
    const featuredContainer = document.getElementById('featured-products');
    const allProductsContainer = document.getElementById('all-products');

    featuredContainer.innerHTML = '<div class="loading">Loading featured products...</div>';
    allProductsContainer.innerHTML = '<div class="loading">Loading products...</div>';
}

function hideLoading() {
    // Loading will be replaced by actual content
}

function showError(message) {
    const featuredContainer = document.getElementById('featured-products');
    const allProductsContainer = document.getElementById('all-products');

    const errorHtml = `<div class="error">${message}</div>`;
    featuredContainer.innerHTML = errorHtml;
    allProductsContainer.innerHTML = errorHtml;
}

function updateCategoryFilter() {
    const categoryFilter = document.getElementById('category-filter');

    // Clear existing options except "All Categories"
    categoryFilter.innerHTML = '<option value="all">All Categories</option>';

    // Add categories from API
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.slug;
        option.textContent = category.name;
        categoryFilter.appendChild(option);
    });
}

// Event listeners
function setupEventListeners() {
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('href').substring(1);
            showSection(section);
        });
    });

    // Mobile menu
    hamburger.addEventListener('click', () => {
        navMenu.classList.toggle('active');
    });

    // Search
    searchInput.addEventListener('input', filterProducts);
    
    // Filters
    categoryFilter.addEventListener('change', filterProducts);
    sortFilter.addEventListener('change', filterProducts);

    // Modal
    closeModal.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Navigation functions
function showSection(sectionName) {
    // Update navigation
    navLinks.forEach(link => link.classList.remove('active'));
    sections.forEach(section => section.classList.remove('active'));
    
    document.getElementById(sectionName).classList.add('active');
    document.querySelector(`[href="#${sectionName}"]`).classList.add('active');
    
    currentSection = sectionName;
    navMenu.classList.remove('active');
    
    // Load section-specific content
    if (sectionName === 'cart') {
        displayCart();
    }
}

// Product display functions
async function displayFeaturedProducts() {
    const featuredContainer = document.getElementById('featured-products');

    try {
        // Try to get featured products from API, fallback to first 6 products
        let featuredProducts = await fetchFeaturedProducts();
        if (featuredProducts.length === 0) {
            featuredProducts = products.slice(0, 6);
        }

        featuredContainer.innerHTML = featuredProducts.map(product => createProductCard(product)).join('');
    } catch (error) {
        console.error('Error displaying featured products:', error);
        featuredContainer.innerHTML = '<div class="error">Failed to load featured products</div>';
    }
}

async function displayAllProducts() {
    const productsContainer = document.getElementById('all-products');

    try {
        if (products.length === 0) {
            await fetchProducts();
        }

        productsContainer.innerHTML = products.map(product => createProductCard(product)).join('');
    } catch (error) {
        console.error('Error displaying products:', error);
        productsContainer.innerHTML = '<div class="error">Failed to load products</div>';
    }
}

function createProductCard(product) {
    // Handle both API format and legacy format
    const rating = product.average_rating || product.rating || 0;
    const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
    const price = product.price || 0;
    const category = product.category || product.category_name || '';
    const description = product.description || product.short_description || '';

    // Track product view
    trackUserBehavior('view', product.id);

    return `
        <div class="product-card" onclick="showProductDetail(${product.id})">
            <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.src='https://via.placeholder.com/400x400?text=No+Image'">
            <div class="product-info">
                <div class="product-rating">
                    <span class="stars">${stars}</span>
                    <span>(${rating.toFixed(1)})</span>
                </div>
                <h3 class="product-name">${product.name}</h3>
                <p class="product-description">${description}</p>
                <div class="product-footer">
                    <span class="product-price">$${parseFloat(price).toFixed(2)}</span>
                    <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id})">
                        🛒 Add
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Product detail modal
async function showProductDetail(productId) {
    let product = products.find(p => p.id === productId);

    if (!product) {
        // Try to fetch product details from API
        try {
            const response = await fetch(`${API_BASE_URL}/products/${productId}/`);
            if (response.ok) {
                product = await response.json();
            }
        } catch (error) {
            console.error('Error fetching product details:', error);
        }
    }

    if (!product) {
        showNotification('Product not found');
        return;
    }

    // Track product click
    trackUserBehavior('click', product.id);

    const rating = product.average_rating || product.rating || 0;
    const stars = '★'.repeat(Math.floor(rating)) + '☆'.repeat(5 - Math.floor(rating));
    const category = product.category || product.category_name || '';
    const price = parseFloat(product.price || 0);

    document.getElementById('product-detail').innerHTML = `
        <div>
            <img src="${product.image}" alt="${product.name}" class="product-detail-image" onerror="this.src='https://via.placeholder.com/400x400?text=No+Image'">
        </div>
        <div class="product-detail-info">
            <h2>${product.name}</h2>
            <div class="product-rating">
                <span class="stars">${stars}</span>
                <span>(${rating.toFixed(1)}) • ${category}</span>
            </div>
            <div class="product-detail-price">$${price.toFixed(2)}</div>
            <p>${product.description}</p>

            <div class="quantity-selector">
                <span>Quantity:</span>
                <button class="quantity-btn" onclick="changeQuantity(-1)">-</button>
                <input type="number" id="quantity" class="quantity-input" value="1" min="1">
                <button class="quantity-btn" onclick="changeQuantity(1)">+</button>
            </div>

            <button class="cta-btn" onclick="addToCartWithQuantity(${product.id})">
                🛒 Add to Cart
            </button>
        </div>
    `;

    modal.style.display = 'block';
}

function changeQuantity(change) {
    const quantityInput = document.getElementById('quantity');
    let quantity = parseInt(quantityInput.value) + change;
    if (quantity < 1) quantity = 1;
    quantityInput.value = quantity;
}

// Cart functions
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        cart.push({...product, cartId: Date.now()});
        updateCartCount();
        saveCart();
        showNotification(`${product.name} added to cart!`);
    }
}

function addToCartWithQuantity(productId) {
    const product = products.find(p => p.id === productId);
    const quantity = parseInt(document.getElementById('quantity').value);
    
    if (product) {
        for (let i = 0; i < quantity; i++) {
            cart.push({...product, cartId: Date.now() + i});
        }
        updateCartCount();
        saveCart();
        modal.style.display = 'none';
        showNotification(`${quantity} x ${product.name} added to cart!`);
    }
}

function removeFromCart(cartId) {
    cart = cart.filter(item => item.cartId !== cartId);
    updateCartCount();
    saveCart();
    displayCart();
}

function updateCartCount() {
    document.getElementById('cart-count').textContent = cart.length;
}

function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

function displayCart() {
    const cartItemsContainer = document.getElementById('cart-items');
    const cartSummaryContainer = document.getElementById('cart-summary');
    
    if (cart.length === 0) {
        cartItemsContainer.innerHTML = `
            <div class="empty-cart">
                <h3>Your cart is empty</h3>
                <p>Start shopping to add items to your cart</p>
                <button class="cta-btn" onclick="showSection('products')">Shop Now</button>
            </div>
        `;
        cartSummaryContainer.innerHTML = '';
        return;
    }
    
    // Group items by product ID
    const groupedItems = cart.reduce((acc, item) => {
        const existing = acc.find(group => group.id === item.id);
        if (existing) {
            existing.quantity += 1;
            existing.items.push(item);
        } else {
            acc.push({
                ...item,
                quantity: 1,
                items: [item]
            });
        }
        return acc;
    }, []);
    
    cartItemsContainer.innerHTML = groupedItems.map(group => `
        <div class="cart-item">
            <img src="${group.image}" alt="${group.name}" class="cart-item-image">
            <div class="cart-item-info">
                <div class="cart-item-name">${group.name}</div>
                <div class="cart-item-price">$${group.price}</div>
            </div>
            <div class="cart-item-controls">
                <button class="quantity-btn" onclick="updateCartQuantity(${group.id}, ${group.quantity - 1})">-</button>
                <span>${group.quantity}</span>
                <button class="quantity-btn" onclick="updateCartQuantity(${group.id}, ${group.quantity + 1})">+</button>
                <button class="quantity-btn" onclick="removeProductFromCart(${group.id})" style="background: #ef4444; color: white; margin-left: 1rem;">×</button>
            </div>
        </div>
    `).join('');
    
    const subtotal = cart.reduce((sum, item) => sum + item.price, 0);
    const tax = subtotal * 0.08;
    const shipping = subtotal > 50 ? 0 : 9.99;
    const total = subtotal + tax + shipping;
    
    cartSummaryContainer.innerHTML = `
        <h3>Order Summary</h3>
        <div class="summary-row">
            <span>Subtotal (${cart.length} items):</span>
            <span>$${subtotal.toFixed(2)}</span>
        </div>
        <div class="summary-row">
            <span>Shipping:</span>
            <span style="color: ${shipping === 0 ? '#10b981' : '#374151'}">${shipping === 0 ? 'Free' : '$' + shipping.toFixed(2)}</span>
        </div>
        <div class="summary-row">
            <span>Tax:</span>
            <span>$${tax.toFixed(2)}</span>
        </div>
        <div class="summary-row summary-total">
            <span>Total:</span>
            <span>$${total.toFixed(2)}</span>
        </div>
        <button class="checkout-btn" onclick="checkout()">
            Proceed to Checkout
        </button>
        <button class="checkout-btn" onclick="clearCart()" style="background: #ef4444; margin-top: 0.5rem;">
            Clear Cart
        </button>
    `;
}

function updateCartQuantity(productId, newQuantity) {
    if (newQuantity <= 0) {
        removeProductFromCart(productId);
        return;
    }
    
    const productItems = cart.filter(item => item.id === productId);
    const currentQuantity = productItems.length;
    
    if (newQuantity > currentQuantity) {
        // Add more items
        const product = products.find(p => p.id === productId);
        for (let i = 0; i < newQuantity - currentQuantity; i++) {
            cart.push({...product, cartId: Date.now() + i});
        }
    } else {
        // Remove items
        const itemsToRemove = currentQuantity - newQuantity;
        for (let i = 0; i < itemsToRemove; i++) {
            const index = cart.findIndex(item => item.id === productId);
            if (index > -1) cart.splice(index, 1);
        }
    }
    
    updateCartCount();
    saveCart();
    displayCart();
}

function removeProductFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartCount();
    saveCart();
    displayCart();
}

function clearCart() {
    cart = [];
    updateCartCount();
    saveCart();
    displayCart();
    showNotification('Cart cleared!');
}

function checkout() {
    if (cart.length === 0) return;
    
    alert('Thank you for your purchase! This is a demo store.');
    clearCart();
}

// Filter and search functions
async function filterProducts() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const sortFilter = document.getElementById('sort-filter');

    const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
    const category = categoryFilter ? categoryFilter.value : 'all';
    const sortBy = sortFilter ? sortFilter.value : 'name';

    // Track search and filter behavior
    if (searchTerm) {
        trackUserBehavior('search', null, { search_query: searchTerm });
    }
    if (category !== 'all') {
        trackUserBehavior('filter', null, { category: category });
    }

    try {
        let filteredProducts;

        // Build API query parameters
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (category !== 'all') params.append('category', category);

        // Try API search/filter first
        if (searchTerm || category !== 'all') {
            const response = await fetch(`${API_BASE_URL}/products/?${params.toString()}`);
            if (response.ok) {
                const data = await response.json();
                filteredProducts = data.results || [];
            } else {
                throw new Error('API request failed');
            }
        } else {
            // No filters, show all products
            if (products.length === 0) {
                await fetchProducts();
            }
            filteredProducts = [...products];
        }

        // Sort products locally
        filteredProducts.sort((a, b) => {
            const priceA = parseFloat(a.price || 0);
            const priceB = parseFloat(b.price || 0);
            const ratingA = a.average_rating || a.rating || 0;
            const ratingB = b.average_rating || b.rating || 0;

            switch (sortBy) {
                case 'price-low':
                    return priceA - priceB;
                case 'price-high':
                    return priceB - priceA;
                case 'rating':
                    return ratingB - ratingA;
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        const productsContainer = document.getElementById('all-products');
        if (filteredProducts.length === 0) {
            productsContainer.innerHTML = `
                <div style="grid-column: 1/-1; text-align: center; padding: 4rem;">
                    <h3>No products found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            `;
        } else {
            productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }
    } catch (error) {
        console.error('Error filtering products:', error);

        // Fallback to local filtering
        let filteredProducts = products.filter(product => {
            const productCategory = product.category || product.category_name || '';
            const matchesSearch = !searchTerm ||
                product.name.toLowerCase().includes(searchTerm) ||
                productCategory.toLowerCase().includes(searchTerm);
            const matchesCategory = category === 'all' || productCategory === category;
            return matchesSearch && matchesCategory;
        });

        const productsContainer = document.getElementById('all-products');
        if (filteredProducts.length === 0) {
            productsContainer.innerHTML = `
                <div style="grid-column: 1/-1; text-align: center; padding: 4rem;">
                    <h3>No products found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            `;
        } else {
            productsContainer.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }
    }
}

// Legacy function names for compatibility
function searchProducts() {
    filterProducts();
}

function filterByCategory() {
    filterProducts();
}

// Notification function
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #9333ea, #2563eb);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        box-shadow: 0 10px 25px rgba(147, 51, 234, 0.3);
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease reverse';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}
