from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()


class Recommendation(models.Model):
    """Store AI-generated recommendations for users"""
    RECOMMENDATION_TYPES = [
        ('collaborative', 'Collaborative Filtering'),
        ('content_based', 'Content-Based Filtering'),
        ('hybrid', 'Hybrid Recommendation'),
        ('trending', 'Trending Products'),
        ('similar', 'Similar Products'),
        ('frequently_bought', 'Frequently Bought Together'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations')
    product = models.ForeignKey('products.Product', on_delete=models.CASCADE)
    recommendation_type = models.CharField(max_length=20, choices=RECOMMENDATION_TYPES)
    score = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(1)])
    reason = models.TextField(blank=True)  # Explanation for the recommendation
    is_active = models.Bo<PERSON>anField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Tracking fields
    view_count = models.PositiveIntegerField(default=0)
    click_count = models.PositiveIntegerField(default=0)
    conversion_count = models.PositiveIntegerField(default=0)  # How many times user bought recommended product
    
    class Meta:
        unique_together = ['user', 'product', 'recommendation_type']
        ordering = ['-score', '-created_at']
        indexes = [
            models.Index(fields=['user', 'recommendation_type', 'is_active']),
            models.Index(fields=['score']),
            models.Index(fields=['created_at']),
        ]
    
    @property
    def click_through_rate(self):
        """Calculate click-through rate"""
        if self.view_count > 0:
            return (self.click_count / self.view_count) * 100
        return 0
    
    @property
    def conversion_rate(self):
        """Calculate conversion rate"""
        if self.click_count > 0:
            return (self.conversion_count / self.click_count) * 100
        return 0
    
    def __str__(self):
        return f"{self.user.email} - {self.product.name} ({self.recommendation_type})"


class UserSimilarity(models.Model):
    """Store user similarity scores for collaborative filtering"""
    user1 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='similarities_as_user1')
    user2 = models.ForeignKey(User, on_delete=models.CASCADE, related_name='similarities_as_user2')
    similarity_score = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(1)])
    algorithm = models.CharField(max_length=50, default='cosine')  # cosine, pearson, jaccard, etc.
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user1', 'user2', 'algorithm']
        indexes = [
            models.Index(fields=['user1', 'similarity_score']),
            models.Index(fields=['user2', 'similarity_score']),
        ]
    
    def __str__(self):
        return f"{self.user1.email} - {self.user2.email}: {self.similarity_score}"


class ProductSimilarity(models.Model):
    """Store product similarity scores for content-based filtering"""
    product1 = models.ForeignKey('products.Product', on_delete=models.CASCADE, related_name='similarities_as_product1')
    product2 = models.ForeignKey('products.Product', on_delete=models.CASCADE, related_name='similarities_as_product2')
    similarity_score = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(1)])
    algorithm = models.CharField(max_length=50, default='cosine')
    features_used = models.JSONField(default=list)  # Which features were used for similarity calculation
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['product1', 'product2', 'algorithm']
        indexes = [
            models.Index(fields=['product1', 'similarity_score']),
            models.Index(fields=['product2', 'similarity_score']),
        ]
    
    def __str__(self):
        return f"{self.product1.name} - {self.product2.name}: {self.similarity_score}"


class RecommendationFeedback(models.Model):
    """Track user feedback on recommendations"""
    FEEDBACK_TYPES = [
        ('like', 'Like'),
        ('dislike', 'Dislike'),
        ('not_interested', 'Not Interested'),
        ('already_owned', 'Already Owned'),
        ('too_expensive', 'Too Expensive'),
        ('wrong_category', 'Wrong Category'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendation_feedback')
    recommendation = models.ForeignKey(Recommendation, on_delete=models.CASCADE, related_name='feedback')
    feedback_type = models.CharField(max_length=20, choices=FEEDBACK_TYPES)
    comment = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'recommendation']
    
    def __str__(self):
        return f"{self.user.email} - {self.feedback_type} - {self.recommendation.product.name}"


class MLModel(models.Model):
    """Track ML model versions and performance"""
    MODEL_TYPES = [
        ('collaborative_filtering', 'Collaborative Filtering'),
        ('content_based', 'Content-Based Filtering'),
        ('hybrid', 'Hybrid Model'),
        ('clustering', 'User Clustering'),
        ('matrix_factorization', 'Matrix Factorization'),
    ]
    
    name = models.CharField(max_length=100)
    model_type = models.CharField(max_length=30, choices=MODEL_TYPES)
    version = models.CharField(max_length=20)
    algorithm = models.CharField(max_length=100)
    parameters = models.JSONField(default=dict)
    training_data_size = models.PositiveIntegerField()
    accuracy_score = models.FloatField(null=True, blank=True)
    precision_score = models.FloatField(null=True, blank=True)
    recall_score = models.FloatField(null=True, blank=True)
    f1_score = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=False)
    model_file_path = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['name', 'version']
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} v{self.version} ({self.model_type})"
